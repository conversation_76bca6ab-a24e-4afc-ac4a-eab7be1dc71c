module.exports = {
  plugins: ['@trivago/prettier-plugin-sort-imports'],
  printWidth: 80,
  tabWidth: 2,
  semi: false,
  singleQuote: true,
  bracketSpacing: true,
  overrides: [
    {
      files: '.prettierrc',
      options: {
        parser: 'json',
      },
    },
  ],
  importOrder: [
    '<THIRD_PARTY_MODULES>',
    '^react(.*)$',
    '@thrift/common',
    '@app/application/(.*)$',
    '@app/domain/(.*)$',
    '@app/resources/(.*)$',
  ],
  importOrderParserPlugins: ['typescript', 'decorators-legacy'],
  importOrderSeparation: true,
  importOrderSortSpecifiers: true,
  // Ignore .yarn
  ignorePath: '.prettierignore',
}
