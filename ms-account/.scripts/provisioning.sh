#!/bin/bash
set -eux
export COREPACK_ENABLE_DOWNwLOAD_PROMPT=0

# Define variables
_credentials=".credentials"
_private_key="${_credentials}/private_key.pem"
_public_key="${_credentials}/public_key.pem"

# Backup current node_modules
[ -d 'node_modules.bkp' ] && rm -vrf node_modules
[ -d 'node_modules' ] && mv -v node_modules node_modules.bkp

# Install packages
git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
yes | corepack enable
yarn install

# Provisioning DATABASE_URL
export DATABASE_URL=$(node -e '
  const { driver, username, password, port, schema } = require("./app.config.json").database
  console.log(`${driver}://${username}:${password}@host.docker.internal:3306/${schema}`)
')

# Provisioning migrations
yarn prisma migrate deploy

# Provisioning seed
_seed_files=($(node -e '
  const seedList = require("./prisma/prisma.seed.json")
  console.log(seedList.join(" "))
'))
for _seed_file in "${_seed_files[@]}"; do
  if [[ "$_seed_file" == *.seed.ts ]]; then
    yarn tsx "./$_seed_file" || { echo "Error executing seed: $_seed_file"; exit 1; }
  fi
done

# Provisioning credentials keys
if [ ! -f "$_private_key" ] || [ ! -f "$_public_key" ]; then
  openssl genpkey -algorithm RSA -out "$_private_key" -pkeyopt rsa_keygen_bits:2048
  openssl rsa -pubout -in "$_private_key" -out "$_public_key"
fi

# Delete cache
rm -vrf .env node_modules

# Recovery current node_modules
[ -d 'node_modules.bkp' ] && mv -v node_modules.bkp node_modules
 
exit 0